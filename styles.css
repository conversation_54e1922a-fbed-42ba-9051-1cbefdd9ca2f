/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f5f2ed;
    background-image: 
        /* Fine linen weave - horizontal threads */
        linear-gradient(0deg, transparent 49.5%, rgba(255,255,255,0.4) 50%, rgba(255,255,255,0.4) 50.5%, transparent 51%),
        linear-gradient(0deg, transparent 74.5%, rgba(255,255,255,0.2) 75%, rgba(255,255,255,0.2) 75.5%, transparent 76%),
        /* Fine linen weave - vertical threads */
        linear-gradient(90deg, transparent 49.5%, rgba(255,255,255,0.35) 50%, rgba(255,255,255,0.35) 50.5%, transparent 51%),
        linear-gradient(90deg, transparent 74.5%, rgba(255,255,255,0.18) 75%, rgba(255,255,255,0.18) 75.5%, transparent 76%),
        /* Crosshatch pattern for linen texture */
        linear-gradient(45deg, transparent 49%, rgba(107, 91, 74, 0.06) 50%, transparent 51%),
        linear-gradient(-45deg, transparent 49%, rgba(107, 91, 74, 0.04) 50%, transparent 51%),
        /* Fine thread variations */
        linear-gradient(30deg, transparent 49.5%, rgba(255,255,255,0.15) 50%, transparent 50.5%),
        linear-gradient(-30deg, transparent 49.5%, rgba(255,255,255,0.12) 50%, transparent 50.5%),
        /* Subtle texture dots for fabric feel */
        radial-gradient(circle at 25% 25%, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0.1) 0.5px, transparent 1px),
        radial-gradient(circle at 75% 75%, rgba(107, 91, 74, 0.05) 0%, transparent 0.8px);
    background-size: 16px 4px, 24px 6px, 4px 16px, 6px 24px, 12px 12px, 12px 12px, 20px 20px, 20px 20px, 8px 8px, 10px 10px;
    background-position: 0 0, 8px 2px, 0 0, 2px 8px, 0 0, 6px 6px, 0 0, 10px 10px, 0 0, 4px 4px;
    color: #6b5b4a;
    line-height: 1.6;
    min-height: 100vh;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: #f5f2ed;
    z-index: 1000;
    padding: 0.8rem 0;
    border: none;
    box-shadow: none;
    border-bottom: none;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo h1 {
    font-size: 1.8rem;
    font-weight: 600;
    color: #6b5b4a;
    margin: 0;
    letter-spacing: 0.05em;
}

.nav-subtitle {
    font-size: 0.65rem;
    color: #a0958a;
    font-weight: 400;
    letter-spacing: 0.1em;
    display: block;
    margin-top: -0.2rem;
}

.nav-links {
    display: flex;
    gap: 3rem;
    align-items: center;
}

.nav-link {
    text-decoration: none;
    color: #6b5b4a;
    font-weight: 400;
    font-size: 1rem;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: #4a3f35;
}

/* Main Content */
.main-content {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 70px 2rem 1rem;
    animation: fadeIn 1s ease-out;
    overflow: hidden;
}

.content-container {
    max-width: 1000px;
    width: 100%;
    display: flex;
    justify-content: center;
}

.content-card {
    background: rgba(255, 255, 255, 0.75);
    backdrop-filter: blur(15px);
    border-radius: 24px;
    padding: 2.5rem 2.5rem;
    text-align: center;
    max-width: 650px;
    box-shadow: 
        0 15px 45px rgba(0, 0, 0, 0.06),
        0 6px 20px rgba(107, 91, 74, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.9);
    position: relative;
    animation: cardFloat 6s ease-in-out infinite;
    transition: all 0.3s ease;
}

.content-card:hover {
    transform: translateY(-5px);
    box-shadow: 
        0 30px 80px rgba(0, 0, 0, 0.12),
        0 12px 35px rgba(107, 91, 74, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

/* Decorative Elements */
.ornament-top, .ornament-bottom {
    font-size: 1.8rem;
    color: rgba(200, 168, 130, 0.6);
    margin: 0 auto;
    animation: sparkle 3s ease-in-out infinite;
}

.ornament-top {
    margin-bottom: 0.8rem;
    animation-delay: 0s;
}

.ornament-bottom {
    margin-top: 1rem;
    animation-delay: 1.5s;
}

.main-title {
    font-family: 'Crimson Text', serif;
    font-size: 3.2rem;
    font-weight: 600;
    color: #6b5b4a;
    margin-bottom: 0.3rem;
    line-height: 0.95;
    letter-spacing: 0.02em;
    text-shadow: 0 2px 4px rgba(107, 91, 74, 0.1);
    animation: titleReveal 1.2s ease-out 0.3s both;
}

.title-underline {
    width: 70px;
    height: 2px;
    background: linear-gradient(90deg, transparent, #c8a882, transparent);
    margin: 0.6rem auto 0.8rem;
    border-radius: 2px;
    animation: underlineGrow 1s ease-out 0.8s both;
}

.sub-title {
    font-family: 'Crimson Text', serif;
    font-size: 2.4rem;
    font-weight: 400;
    color: #6b5b4a;
    margin-bottom: 1.2rem;
    line-height: 0.95;
    letter-spacing: 0.01em;
    opacity: 0.9;
    animation: subtitleReveal 1.2s ease-out 0.5s both;
}

.description {
    font-size: 1rem;
    color: #6b5b4a;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    max-width: 550px;
    margin-left: auto;
    margin-right: auto;
    letter-spacing: 0.01em;
    animation: descriptionFade 1.2s ease-out 0.9s both;
}

.button-container {
    margin-top: 1rem;
    animation: buttonSlideUp 1.2s ease-out 1.1s both;
}

.sign-in-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.6rem;
    background: linear-gradient(135deg, #c8a882 0%, #b8966f 100%);
    color: white;
    padding: 1rem 2.5rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.95rem;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    letter-spacing: 0.02em;
    position: relative;
    overflow: hidden;
    box-shadow: 
        0 6px 20px rgba(200, 168, 130, 0.25),
        0 3px 10px rgba(200, 168, 130, 0.15);
}

.sign-in-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.6s ease;
}

.sign-in-btn:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 
        0 15px 40px rgba(200, 168, 130, 0.4),
        0 8px 20px rgba(200, 168, 130, 0.3);
}

.sign-in-btn:hover::before {
    left: 100%;
}

.sign-in-btn:active {
    transform: translateY(-1px) scale(1.01);
}

.btn-text {
    transition: transform 0.3s ease;
}

.btn-arrow {
    transition: transform 0.3s ease;
    font-size: 1.1rem;
}

.sign-in-btn:hover .btn-arrow {
    transform: translateX(4px);
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes cardFloat {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-8px);
    }
}

@keyframes sparkle {
    0%, 100% {
        opacity: 0.6;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.1);
    }
}

@keyframes titleReveal {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes subtitleReveal {
    from {
        opacity: 0;
        transform: translateY(25px);
    }
    to {
        opacity: 0.9;
        transform: translateY(0);
    }
}

@keyframes underlineGrow {
    from {
        width: 0;
        opacity: 0;
    }
    to {
        width: 80px;
        opacity: 1;
    }
}

@keyframes descriptionFade {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes buttonSlideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-container {
        padding: 0 1rem;
    }
    
    .nav-links {
        gap: 1.5rem;
    }
    
    .nav-link {
        font-size: 0.9rem;
    }
    
    .main-content {
        padding: 80px 1rem 2rem;
    }
    
    .content-card {
        padding: 3.5rem 2.5rem;
        margin: 0 1rem;
        border-radius: 24px;
    }
    
    .main-title {
        font-size: 3.2rem;
    }
    
    .sub-title {
        font-size: 2.5rem;
        margin-bottom: 1.8rem;
    }
    
    .arabic-quote {
        margin: 2rem auto;
        padding: 1.2rem;
    }
    
    .quote-text {
        font-size: 1.1rem;
    }
    
    .description {
        font-size: 1.05rem;
        margin-bottom: 2.5rem;
        line-height: 1.7;
    }
    
    .sign-in-btn {
        padding: 1.1rem 2.5rem;
        font-size: 1rem;
    }
    
    .ornament-top, .ornament-bottom {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .nav-logo h1 {
        font-size: 1.5rem;
    }
    
    .nav-subtitle {
        font-size: 0.6rem;
    }
    
    .nav-links {
        gap: 1rem;
    }
    
    .nav-link {
        font-size: 0.85rem;
    }
    
    .content-card {
        padding: 2.5rem 1.8rem;
        margin: 0 0.5rem;
    }
    
    .main-title {
        font-size: 2.5rem;
    }
    
    .sub-title {
        font-size: 2rem;
        margin-bottom: 1.5rem;
    }
    
    .title-underline {
        width: 60px;
        margin: 0.8rem auto 1.2rem;
    }
    
    .arabic-quote {
        margin: 1.5rem auto;
        padding: 1rem;
    }
    
    .quote-text {
        font-size: 1rem;
    }
    
    .quote-translation {
        font-size: 0.85rem;
    }
    
    .description {
        font-size: 1rem;
        margin-bottom: 2rem;
    }
    
    .sign-in-btn {
        padding: 1rem 2rem;
        font-size: 0.95rem;
        gap: 0.6rem;
    }
    
    .ornament-top, .ornament-bottom {
        font-size: 1.3rem;
    }
    
    .ornament-top {
        margin-bottom: 1rem;
    }
    
    .ornament-bottom {
        margin-top: 1.5rem;
    }
}

/* ========================================
   COURSES PAGE STYLES
======================================== */

/* Navigation Updates for Courses Page */
.nav-logo h1 a {
    text-decoration: none;
    color: inherit;
    transition: color 0.3s ease;
}

.nav-logo h1 a:hover {
    color: #c8a882;
}

.nav-link.active {
    color: #c8a882;
    font-weight: 600;
}

/* Courses Main Layout */
.courses-main {
    min-height: 100vh;
    padding: 100px 2rem 3rem;
    animation: fadeIn 1s ease-out;
}

.courses-container {
    max-width: 1200px;
    margin: 0 auto;
}

/* Courses Header */
.courses-header {
    text-align: center;
    margin-bottom: 4rem;
    animation: headerSlideDown 1.2s ease-out;
}

.page-title {
    font-family: 'Crimson Text', serif;
    font-size: 3.5rem;
    font-weight: 600;
    color: #6b5b4a;
    margin-bottom: 1rem;
    line-height: 1.1;
    letter-spacing: 0.02em;
    text-shadow: 0 2px 4px rgba(107, 91, 74, 0.1);
}

.page-subtitle {
    font-size: 1.2rem;
    color: #a0958a;
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto;
    letter-spacing: 0.01em;
}

/* Courses Grid */
.courses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2.5rem;
    margin-bottom: 4rem;
}

/* Course Cards */
.course-card {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(15px);
    border-radius: 24px;
    padding: 2.5rem;
    text-align: center;
    position: relative;
    box-shadow: 
        0 15px 45px rgba(0, 0, 0, 0.08),
        0 6px 20px rgba(107, 91, 74, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.9);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    animation: cardSlideUp 0.8s ease-out both;
}

.course-card:nth-child(1) { animation-delay: 0.2s; }
.course-card:nth-child(2) { animation-delay: 0.4s; }
.course-card:nth-child(3) { animation-delay: 0.6s; }

.course-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 
        0 25px 60px rgba(0, 0, 0, 0.12),
        0 10px 30px rgba(107, 91, 74, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

/* Course Icons */
.course-icon {
    font-size: 3rem;
    margin: 1.5rem 0 1rem;
    animation: iconFloat 3s ease-in-out infinite;
}

.beginner-plant {
    filter: sepia(100%) saturate(150%) hue-rotate(25deg) brightness(0.9);
}

.intermediate-plant {
    filter: sepia(100%) saturate(120%) hue-rotate(30deg) brightness(0.8);
}

.advanced-plant {
    filter: sepia(100%) saturate(100%) hue-rotate(35deg) brightness(0.7);
}

.course-card:nth-child(1) .course-icon { animation-delay: 0s; }
.course-card:nth-child(2) .course-icon { animation-delay: 1s; }
.course-card:nth-child(3) .course-icon { animation-delay: 2s; }

/* Course Content */
.course-title {
    font-family: 'Crimson Text', serif;
    font-size: 1.8rem;
    font-weight: 600;
    color: #6b5b4a;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.course-description {
    font-size: 1rem;
    color: #6b5b4a;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    opacity: 0.9;
}

/* Course Stats */
.course-stats {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: rgba(200, 168, 130, 0.05);
    border-radius: 12px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: #6b5b4a;
}

.stat-icon {
    font-size: 1.1rem;
}

/* Progress Section */
.progress-section {
    margin: 1.5rem 0;
}

.progress-label {
    font-size: 0.9rem;
    color: #a0958a;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(200, 168, 130, 0.2);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #c8a882, #b8966f);
    border-radius: 4px;
    transition: width 0.8s ease;
}

.progress-text {
    font-size: 0.85rem;
    color: #a0958a;
}

/* Course Buttons */
.course-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.6rem;
    background: linear-gradient(135deg, #c8a882 0%, #b8966f 100%);
    color: white;
    padding: 1rem 2rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.95rem;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    letter-spacing: 0.02em;
    position: relative;
    overflow: hidden;
    box-shadow: 
        0 6px 20px rgba(200, 168, 130, 0.25),
        0 3px 10px rgba(200, 168, 130, 0.15);
    margin-top: 1rem;
}

.course-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.6s ease;
}

.course-btn:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 
        0 12px 30px rgba(200, 168, 130, 0.35),
        0 6px 15px rgba(200, 168, 130, 0.25);
}

.course-btn:hover::before {
    left: 100%;
}

.course-btn:hover .btn-arrow {
    transform: translateX(4px);
}

.course-btn .btn-arrow {
    transition: transform 0.3s ease;
}

/* Call to Action Section */
.cta-section {
    text-align: center;
    padding: 3rem 2rem;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 24px;
    margin-top: 3rem;
    animation: ctaSlideUp 1.2s ease-out 0.8s both;
}

.cta-title {
    font-family: 'Crimson Text', serif;
    font-size: 2.5rem;
    font-weight: 600;
    color: #6b5b4a;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.cta-description {
    font-size: 1.1rem;
    color: #6b5b4a;
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto 2rem;
    opacity: 0.9;
}

.cta-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.8rem;
    background: linear-gradient(135deg, #6b5b4a 0%, #5a4a3a 100%);
    color: white;
    padding: 1.2rem 2.5rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    letter-spacing: 0.02em;
    box-shadow: 
        0 8px 25px rgba(107, 91, 74, 0.3),
        0 4px 12px rgba(107, 91, 74, 0.2);
}

.cta-btn:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 
        0 15px 40px rgba(107, 91, 74, 0.4),
        0 8px 20px rgba(107, 91, 74, 0.3);
}

.cta-btn:hover .btn-arrow {
    transform: translateX(4px);
}

/* New Animations for Courses Page */
@keyframes headerSlideDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes cardSlideUp {
    from {
        opacity: 0;
        transform: translateY(40px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes iconFloat {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-8px);
    }
}

@keyframes ctaSlideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Additional Responsive Design for Courses */
@media (max-width: 768px) {
    .courses-main {
        padding: 80px 1rem 2rem;
    }
    
    .page-title {
        font-size: 2.8rem;
    }
    
    .courses-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .course-card {
        padding: 2rem;
    }
    
    .course-stats {
        flex-direction: column;
        gap: 1rem;
    }
    
    .cta-section {
        padding: 2rem 1.5rem;
    }
    
    .cta-title {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .page-title {
        font-size: 2.2rem;
    }
    
    .course-card {
        padding: 1.5rem;
    }
    
    .course-title {
        font-size: 1.5rem;
    }
    
    .course-btn, .cta-btn {
        padding: 0.9rem 1.8rem;
        font-size: 0.9rem;
    }
}

/* ===== ADMIN STYLES ===== */

/* Admin Main Layout */
.admin-main {
    padding: 90px 2rem 2rem;
    min-height: 100vh;
}

.admin-container {
    max-width: 1200px;
    margin: 0 auto;
}

.admin-header {
    text-align: center;
    margin-bottom: 3rem;
    animation: fadeIn 0.8s ease-out;
}

.section-title {
    font-family: 'Crimson Text', serif;
    font-size: 1.8rem;
    color: #6b5b4a;
    margin-bottom: 2rem;
    position: relative;
}

.section-title::after {
    content: '';
    display: block;
    width: 50px;
    height: 2px;
    background: #c8a882;
    margin: 0.5rem auto;
    border-radius: 2px;
}

/* Form Styles */
.form-section {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(15px);
    border-radius: 16px;
    padding: 2.5rem;
    margin-bottom: 3rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(255, 255, 255, 0.9);
    animation: slideUp 0.8s ease-out;
}

.course-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-label {
    font-weight: 500;
    color: #6b5b4a;
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
}

.form-input,
.form-select,
.form-textarea {
    padding: 0.75rem 1rem;
    border: 2px solid rgba(200, 168, 130, 0.3);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.9);
    font-size: 1rem;
    color: #6b5b4a;
    transition: all 0.3s ease;
    font-family: 'Inter', sans-serif;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: #c8a882;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 0 0 3px rgba(200, 168, 130, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

.checkbox-group {
    margin: 1rem 0;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    font-size: 0.95rem;
    color: #6b5b4a;
}

.form-checkbox {
    width: 18px;
    height: 18px;
    accent-color: #c8a882;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 1rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(200, 168, 130, 0.2);
}

.btn-secondary {
    background: rgba(107, 91, 74, 0.1);
    color: #6b5b4a;
    border: 2px solid rgba(107, 91, 74, 0.3);
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-secondary:hover {
    background: rgba(107, 91, 74, 0.15);
    border-color: rgba(107, 91, 74, 0.5);
}

/* Course List Styles */
.courses-section {
    animation: slideUp 0.8s ease-out 0.2s both;
}

.courses-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.course-item {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(15px);
    border-radius: 12px;
    padding: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.9);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
    transition: all 0.3s ease;
}

.course-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
}

.course-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.course-title {
    font-family: 'Crimson Text', serif;
    font-size: 1.4rem;
    color: #6b5b4a;
    margin-bottom: 0.5rem;
}

.course-meta {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.course-meta span {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.course-level {
    background: rgba(200, 168, 130, 0.2);
    color: #6b5b4a;
}

.course-lessons,
.course-duration {
    background: rgba(107, 91, 74, 0.1);
    color: #6b5b4a;
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.status-badge.published {
    background: rgba(34, 197, 94, 0.1);
    color: rgb(22, 163, 74);
    border: 1px solid rgba(34, 197, 94, 0.2);
}

.status-badge.draft {
    background: rgba(251, 191, 36, 0.1);
    color: rgb(217, 119, 6);
    border: 1px solid rgba(251, 191, 36, 0.2);
}

.course-description p {
    color: #6b5b4a;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.course-actions {
    display: flex;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.btn-action {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    font-size: 0.85rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-action.edit {
    background: rgba(59, 130, 246, 0.1);
    color: rgb(37, 99, 235);
}

.btn-action.edit:hover {
    background: rgba(59, 130, 246, 0.2);
}

.btn-action.publish {
    background: rgba(34, 197, 94, 0.1);
    color: rgb(22, 163, 74);
}

.btn-action.publish:hover {
    background: rgba(34, 197, 94, 0.2);
}

.btn-action.unpublish {
    background: rgba(251, 191, 36, 0.1);
    color: rgb(217, 119, 6);
}

.btn-action.unpublish:hover {
    background: rgba(251, 191, 36, 0.2);
}

.btn-action.delete {
    background: rgba(239, 68, 68, 0.1);
    color: rgb(220, 38, 38);
}

.btn-action.delete:hover {
    background: rgba(239, 68, 68, 0.2);
}

.course-timestamps {
    display: flex;
    gap: 1rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(200, 168, 130, 0.2);
}

.course-timestamps small {
    color: rgba(107, 91, 74, 0.7);
    font-size: 0.8rem;
}

.no-courses {
    text-align: center;
    padding: 3rem;
    color: rgba(107, 91, 74, 0.7);
}

/* Message Styles */
.message {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    margin: 1rem 0;
    font-weight: 500;
}

.loading-message {
    background: rgba(59, 130, 246, 0.1);
    color: rgb(37, 99, 235);
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.success-message {
    background: rgba(34, 197, 94, 0.1);
    color: rgb(22, 163, 74);
    border: 1px solid rgba(34, 197, 94, 0.2);
}

.error-message {
    background: rgba(239, 68, 68, 0.1);
    color: rgb(220, 38, 38);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.loading-spinner {
    animation: spin 1s linear infinite;
    font-size: 1.2rem;
}

.message-icon {
    font-weight: bold;
    font-size: 1.1rem;
}

/* Animations */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design for Admin */
@media (max-width: 768px) {
    .admin-container {
        padding: 0 1rem;
    }
    
    .form-section {
        padding: 1.5rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .course-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .course-actions {
        flex-wrap: wrap;
    }
    
    .course-timestamps {
        flex-direction: column;
        gap: 0.5rem;
    }
}

/* Loading and Error States for Courses */
.loading-courses,
.error-courses,
.no-courses {
    grid-column: 1 / -1;
    text-align: center;
    padding: 4rem 2rem;
    color: #6b5b4a;
}

.loading-courses .loading-spinner {
    font-size: 2rem;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

.error-courses .retry-btn {
    background: #c8a882;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    margin-top: 1rem;
    transition: all 0.3s ease;
}

.error-courses .retry-btn:hover {
    background: #b8966f;
    transform: translateY(-2px);
}

/* ===========================================
   LESSON SYSTEM STYLING
   =========================================== */

/* Course Detail Page */
.course-detail-main {
    padding: 90px 2rem 2rem;
    min-height: 100vh;
    background: inherit;
}

.course-detail-container {
    max-width: 1200px;
    margin: 0 auto;
}

.back-navigation {
    margin-bottom: 2rem;
}

.back-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem 1.5rem;
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(200, 168, 130, 0.3);
    border-radius: 12px;
    text-decoration: none;
    color: #6b5b4a;
    font-weight: 500;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.back-btn:hover {
    background: rgba(255, 255, 255, 0.95);
    transform: translateX(-5px);
    box-shadow: 0 4px 15px rgba(107, 91, 74, 0.1);
}

.back-arrow {
    font-size: 1.2rem;
    transition: transform 0.3s ease;
}

.back-btn:hover .back-arrow {
    transform: translateX(-3px);
}

.course-detail-header {
    text-align: center;
    margin-bottom: 3rem;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 20px;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.9);
}

.course-level-badge {
    display: inline-block;
    padding: 0.5rem 1.2rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 1rem;
}

.course-level-badge.beginner {
    background: rgba(34, 197, 94, 0.1);
    color: #059669;
    border: 1px solid rgba(34, 197, 94, 0.2);
}

.course-level-badge.intermediate {
    background: rgba(251, 191, 36, 0.1);
    color: #d97706;
    border: 1px solid rgba(251, 191, 36, 0.2);
}

.course-level-badge.advanced {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.course-detail-title {
    font-family: 'Crimson Text', serif;
    font-size: 2.8rem;
    font-weight: 600;
    color: #6b5b4a;
    margin-bottom: 0.5rem;
    line-height: 1.1;
}

.course-detail-description {
    font-size: 1.1rem;
    color: #6b5b4a;
    line-height: 1.7;
    max-width: 700px;
    margin: 0 auto 2rem;
}

.course-detail-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 12px;
    border: 1px solid rgba(200, 168, 130, 0.2);
}

.stat-icon {
    font-size: 1.5rem;
    display: block;
    margin-bottom: 0.5rem;
}

.stat-label {
    display: block;
    font-size: 0.9rem;
    color: #a0958a;
    font-weight: 500;
    margin-bottom: 0.3rem;
}

.stat-value {
    font-size: 1.3rem;
    font-weight: 600;
    color: #6b5b4a;
}

.course-progress-section {
    margin-top: 1rem;
}

.progress-label {
    font-size: 0.9rem;
    color: #6b5b4a;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.progress-bar-container {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.progress-bar {
    flex: 1;
    height: 8px;
    background: rgba(200, 168, 130, 0.2);
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar.small {
    height: 6px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #c8a882, #d4b896);
    border-radius: 4px;
    transition: width 0.8s ease;
}

.progress-percentage {
    font-size: 0.9rem;
    font-weight: 600;
    color: #6b5b4a;
    min-width: 40px;
}

/* Lessons Section */
.lessons-section {
    margin-bottom: 3rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.section-title {
    font-family: 'Crimson Text', serif;
    font-size: 2rem;
    font-weight: 600;
    color: #6b5b4a;
}

.lesson-filter {
    display: flex;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    padding: 0.3rem;
    border: 1px solid rgba(200, 168, 130, 0.2);
}

.filter-btn {
    padding: 0.5rem 1rem;
    border: none;
    background: transparent;
    color: #6b5b4a;
    font-size: 0.9rem;
    font-weight: 500;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-btn.active,
.filter-btn:hover {
    background: #c8a882;
    color: white;
}

.lessons-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.lesson-item {
    display: flex;
    align-items: center;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 16px;
    border: 1px solid rgba(200, 168, 130, 0.2);
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.lesson-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(107, 91, 74, 0.1);
}

.lesson-item.completed {
    background: rgba(34, 197, 94, 0.05);
    border-color: rgba(34, 197, 94, 0.2);
}

.lesson-item.in-progress {
    background: rgba(251, 191, 36, 0.05);
    border-color: rgba(251, 191, 36, 0.2);
}

.lesson-item.locked {
    opacity: 0.6;
    background: rgba(0, 0, 0, 0.02);
}

.lesson-number {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    font-weight: 600;
    margin-right: 1.5rem;
    flex-shrink: 0;
}

.lesson-item:not(.completed):not(.locked) .lesson-number {
    background: rgba(200, 168, 130, 0.1);
    color: #6b5b4a;
    border: 2px solid rgba(200, 168, 130, 0.3);
}

.lesson-item.completed .lesson-number {
    background: rgba(34, 197, 94, 0.1);
    color: #059669;
    border: 2px solid rgba(34, 197, 94, 0.3);
}

.lesson-item.locked .lesson-number {
    background: rgba(0, 0, 0, 0.05);
    color: #a0958a;
    border: 2px solid rgba(0, 0, 0, 0.1);
}

.lesson-content {
    flex: 1;
    min-width: 0;
}

.lesson-title-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.75rem;
    gap: 1rem;
}

.lesson-header {
    margin-bottom: 0.5rem;
}

.lesson-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #6b5b4a;
    margin: 0;
    line-height: 1.3;
    flex: 1;
}

.lesson-meta {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    flex-shrink: 0;
}

.lesson-description {
    margin-bottom: 1rem;
    color: #6b5b4a;
    line-height: 1.6;
    font-size: 0.95rem;
}

.lesson-progress-row {
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.progress-container {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
}

.lesson-progress {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.progress-text {
    font-size: 0.9rem;
    color: #6b5b4a;
    font-weight: 500;
    min-width: 80px;
}

.lesson-duration {
    font-size: 0.9rem;
    color: #a0958a;
    font-weight: 500;
    white-space: nowrap;
}

.lesson-actions {
    margin-left: 1.5rem;
    flex-shrink: 0;
}

.lesson-btn {
    padding: 0.8rem 1.5rem;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.lesson-btn.start {
    background: #c8a882;
    color: white;
}

.lesson-btn.review {
    background: rgba(200, 168, 130, 0.1);
    color: #6b5b4a;
    border: 1px solid rgba(200, 168, 130, 0.3);
}

.lesson-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(107, 91, 74, 0.2);
}

.locked-message {
    font-size: 0.9rem;
    color: #a0958a;
    font-style: italic;
    text-align: center;
    padding: 1rem;
}

/* Admin Lesson Management Enhancements */
.btn-action.manage-lessons {
    background: rgba(147, 51, 234, 0.1);
    color: #7c3aed;
}

.btn-action.manage-lessons:hover {
    background: rgba(147, 51, 234, 0.2);
}

.lessons-management-section {
    margin-top: 2rem;
}

.selected-course-info {
    margin-bottom: 2rem;
}

.selected-course-card {
    padding: 1rem 1.5rem;
    background: rgba(200, 168, 130, 0.1);
    border-radius: 12px;
    border: 1px solid rgba(200, 168, 130, 0.3);
}

.selected-course-card h3 {
    color: #6b5b4a;
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
}

.selected-course-card p {
    color: #a0958a;
    margin: 0;
    font-size: 0.9rem;
}

.subsection-title {
    font-family: 'Crimson Text', serif;
    font-size: 1.5rem;
    color: #6b5b4a;
    margin-bottom: 1rem;
    border-bottom: 2px solid rgba(200, 168, 130, 0.2);
    padding-bottom: 0.5rem;
}

.lesson-form {
    background: rgba(255, 255, 255, 0.8);
    border-radius: 16px;
    padding: 2rem;
    border: 1px solid rgba(200, 168, 130, 0.2);
}

/* Integrated Materials Section */
.materials-section {
    margin: 2rem 0;
    padding: 1.5rem;
    background: rgba(245, 242, 237, 0.5);
    border-radius: 12px;
    border: 1px solid rgba(200, 168, 130, 0.2);
}

.materials-title {
    font-family: 'Crimson Text', serif;
    font-size: 1.3rem;
    color: #6b5b4a;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.materials-description {
    color: #a0958a;
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
    font-style: italic;
}

/* Material Builder */
.material-builder {
    background: rgba(255, 255, 255, 0.8);
    border-radius: 16px;
    padding: 2rem;
    border: 1px solid rgba(200, 168, 130, 0.2);
}

.material-controls {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.material-btn {
    padding: 0.8rem 1.2rem;
    background: rgba(200, 168, 130, 0.1);
    border: 1px solid rgba(200, 168, 130, 0.3);
    border-radius: 8px;
    color: #6b5b4a;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.material-btn:hover {
    background: rgba(200, 168, 130, 0.2);
    transform: translateY(-1px);
}

.material-editor {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(200, 168, 130, 0.2);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.material-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.material-controls .btn-small {
    padding: 0.3rem 0.6rem;
    font-size: 0.8rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin-left: 0.2rem;
}

.btn-small.delete {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
}

.material-title-input,
.material-content-input {
    width: 100%;
    border: 1px solid rgba(200, 168, 130, 0.3);
    border-radius: 4px;
    padding: 0.5rem;
    margin-bottom: 0.5rem;
    font-family: inherit;
}

.material-content-input {
    resize: vertical;
    min-height: 80px;
}

/* Image Upload Styles */
.image-upload-section {
    margin: 0.5rem 0;
}

.material-image-input {
    display: none;
}

.image-upload-label {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem 1.2rem;
    background: rgba(200, 168, 130, 0.1);
    border: 2px dashed rgba(200, 168, 130, 0.4);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #6b5b4a;
    font-weight: 500;
}

.image-upload-label:hover {
    background: rgba(200, 168, 130, 0.2);
    border-color: rgba(200, 168, 130, 0.6);
    transform: translateY(-1px);
}

.upload-icon {
    font-size: 1.2rem;
}

.image-preview {
    margin-top: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    border: 1px solid rgba(200, 168, 130, 0.2);
}

.preview-image {
    max-width: 200px;
    max-height: 150px;
    border-radius: 6px;
    object-fit: cover;
    display: block;
    margin-bottom: 0.5rem;
}

.image-name {
    font-size: 0.9rem;
    color: #6b5b4a;
    font-weight: 500;
}

/* Image Sizing Controls */
.image-sizing-controls {
    margin-top: 1rem;
    padding: 1rem;
    background: rgba(200, 168, 130, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(200, 168, 130, 0.1);
}

.sizing-section {
    margin-bottom: 1rem;
}

.sizing-section:last-child {
    margin-bottom: 0;
}

.sizing-label {
    display: block;
    font-size: 0.85rem;
    font-weight: 600;
    color: #6b5b4a;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Slider Control */
.size-slider-container {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    margin-bottom: 0.8rem;
}

.size-slider {
    flex: 1;
    height: 6px;
    border-radius: 3px;
    background: rgba(200, 168, 130, 0.2);
    outline: none;
    -webkit-appearance: none;
    appearance: none;
}

.size-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #c8a882;
    cursor: pointer;
    border: 2px solid #ffffff;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
}

.size-slider::-webkit-slider-thumb:hover {
    background: #ba936d;
    transform: scale(1.1);
}

.size-slider::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #c8a882;
    cursor: pointer;
    border: 2px solid #ffffff;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
}

.size-value {
    min-width: 50px;
    text-align: center;
    font-size: 0.85rem;
    font-weight: 600;
    color: #6b5b4a;
    background: rgba(255, 255, 255, 0.8);
    padding: 0.3rem 0.6rem;
    border-radius: 12px;
    border: 1px solid rgba(200, 168, 130, 0.2);
}

/* Preset Buttons */
.size-presets {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.size-preset-btn {
    padding: 0.4rem 0.8rem;
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(200, 168, 130, 0.3);
    border-radius: 16px;
    font-size: 0.8rem;
    font-weight: 500;
    color: #6b5b4a;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.size-preset-btn:hover {
    background: rgba(200, 168, 130, 0.1);
    border-color: rgba(200, 168, 130, 0.5);
    transform: translateY(-1px);
}

.size-preset-btn.active {
    background: #c8a882;
    color: white;
    border-color: #c8a882;
    box-shadow: 0 2px 6px rgba(200, 168, 130, 0.3);
}

/* Responsive Image Container */
.responsive-image-container {
    width: 100%;
    max-width: 100%;
    margin: 0.5rem 0;
    transition: all 0.3s ease;
    overflow: hidden;
    border-radius: 6px;
}

.responsive-image-container.size-small {
    max-width: 200px;
}

.responsive-image-container.size-medium {
    max-width: 400px;
}

.responsive-image-container.size-large {
    max-width: 600px;
}

.responsive-image-container.size-full {
    max-width: 100%;
}

.resizable-image {
    width: 100%;
    height: auto;
    display: block;
    transition: all 0.3s ease;
    border-radius: 6px;
}

.custom-size-container {
    transform-origin: top left;
    transition: transform 0.3s ease;
}

/* Interactive Quiz Builder Styles */
.quiz-builder {
    margin: 0.5rem 0;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 12px;
    border: 1px solid rgba(200, 168, 130, 0.2);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.quiz-builder-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid rgba(200, 168, 130, 0.2);
}

.quiz-type-selector {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.quiz-type-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    background: rgba(200, 168, 130, 0.1);
    border: 1px solid rgba(200, 168, 130, 0.3);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9rem;
    font-weight: 500;
}

.quiz-type-option:hover {
    background: rgba(200, 168, 130, 0.2);
    border-color: rgba(200, 168, 130, 0.5);
}

.quiz-type-option.active {
    background: #c8a882;
    color: white;
    border-color: #c8a882;
}

.quiz-type-radio {
    margin: 0;
}

.quiz-question-input {
    width: 100%;
    padding: 1rem;
    border: 2px solid rgba(200, 168, 130, 0.3);
    border-radius: 8px;
    font-size: 1rem;
    font-family: inherit;
    resize: vertical;
    min-height: 80px;
    margin-bottom: 1.5rem;
    transition: border-color 0.3s ease;
}

.quiz-question-input:focus {
    outline: none;
    border-color: rgba(200, 168, 130, 0.6);
    box-shadow: 0 0 0 3px rgba(200, 168, 130, 0.1);
}

.quiz-options-section {
    margin-bottom: 1.5rem;
}

.quiz-options-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.quiz-options-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #6b5b4a;
    margin: 0;
}

.add-option-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: rgba(200, 168, 130, 0.1);
    border: 1px solid rgba(200, 168, 130, 0.4);
    border-radius: 6px;
    color: #6b5b4a;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.add-option-btn:hover {
    background: rgba(200, 168, 130, 0.2);
    border-color: rgba(200, 168, 130, 0.6);
    transform: translateY(-1px);
}

.quiz-options-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.quiz-option-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(200, 168, 130, 0.2);
    border-radius: 8px;
    transition: all 0.2s ease;
}

.quiz-option-item:hover {
    background: rgba(200, 168, 130, 0.05);
    border-color: rgba(200, 168, 130, 0.3);
}

.option-label {
    font-weight: 600;
    color: #6b5b4a;
    min-width: 30px;
    text-align: center;
    background: rgba(200, 168, 130, 0.2);
    padding: 0.5rem;
    border-radius: 6px;
    font-size: 0.9rem;
}

.option-input {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid rgba(200, 168, 130, 0.3);
    border-radius: 6px;
    font-size: 0.9rem;
    font-family: inherit;
    transition: border-color 0.3s ease;
}

.option-input:focus {
    outline: none;
    border-color: rgba(200, 168, 130, 0.6);
    box-shadow: 0 0 0 3px rgba(200, 168, 130, 0.1);
}

.option-correct-checkbox {
    position: relative;
    width: 24px;
    height: 24px;
    cursor: pointer;
}

.option-correct-checkbox input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

.option-correct-checkbox .checkmark {
    position: absolute;
    top: 0;
    left: 0;
    height: 24px;
    width: 24px;
    background: rgba(200, 168, 130, 0.1);
    border: 2px solid rgba(200, 168, 130, 0.4);
    border-radius: 6px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.option-correct-checkbox input[type="checkbox"]:checked ~ .checkmark {
    background: #10b981;
    border-color: #10b981;
    color: white;
}

.option-correct-checkbox .checkmark:after {
    content: "✓";
    position: absolute;
    display: none;
}

.option-correct-checkbox input[type="checkbox"]:checked ~ .checkmark:after {
    display: block;
}

.remove-option-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border-radius: 6px;
    cursor: pointer;
    font-size: 1.2rem;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.remove-option-btn:hover {
    background: rgba(239, 68, 68, 0.2);
    transform: scale(1.1);
}

.quiz-explanation-section {
    margin-top: 1.5rem;
}

.quiz-explanation-label {
    display: block;
    font-size: 1rem;
    font-weight: 600;
    color: #6b5b4a;
    margin-bottom: 0.5rem;
}

.quiz-explanation-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid rgba(200, 168, 130, 0.3);
    border-radius: 6px;
    font-size: 0.9rem;
    font-family: inherit;
    resize: vertical;
    min-height: 60px;
    transition: border-color 0.3s ease;
}

.quiz-explanation-input:focus {
    outline: none;
    border-color: rgba(200, 168, 130, 0.6);
    box-shadow: 0 0 0 3px rgba(200, 168, 130, 0.1);
}

.quiz-preview-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, #c8a882, #ba936d);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.quiz-preview-btn:hover {
    background: linear-gradient(135deg, #ba936d, #a67c52);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Quiz Preview Modal Styles */
.quiz-preview-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    box-sizing: border-box;
}

.quiz-preview-content {
    background: var(--bg-primary);
    border-radius: 16px;
    padding: 2rem;
    max-width: 600px;
    width: 100%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.quiz-preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(200, 168, 130, 0.2);
}

.quiz-preview-title {
    font-size: 1.4rem;
    font-weight: 600;
    color: #6b5b4a;
    margin: 0;
}

.quiz-preview-close {
    width: 32px;
    height: 32px;
    border: none;
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1.2rem;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.quiz-preview-close:hover {
    background: rgba(239, 68, 68, 0.2);
    transform: scale(1.1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .quiz-builder {
        padding: 1rem;
    }
    
    .quiz-builder-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .quiz-type-selector {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .quiz-option-item {
        flex-direction: column;
        gap: 0.75rem;
        align-items: stretch;
    }
    
    .option-label {
        text-align: left;
        min-width: auto;
    }
    
    .quiz-preview-modal {
        padding: 1rem;
    }
    
    .quiz-preview-content {
        padding: 1.5rem;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .course-detail-container,
    .lesson-container {
        padding: 0 1rem;
    }
    
    .course-detail-title {
        font-size: 2.2rem;
    }
    
    .lesson-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .lesson-number {
        margin-right: 0;
        align-self: flex-start;
    }
    
    .lesson-actions {
        margin-left: 0;
        align-self: stretch;
    }
    
    .lesson-btn {
        width: 100%;
        justify-content: center;
    }
    
    .section-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .material-controls {
        flex-direction: column;
    }
    
    .material-btn {
        width: 100%;
        text-align: center;
    }
}

/* ========================================
   Lesson Page Core Structure
   ======================================== */

.lesson-main {
    min-height: 100vh;
    background: var(--background-primary);
    padding-top: 80px; /* Account for navbar */
}

.lesson-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    position: relative;
}

/* Lesson Header */
.lesson-header {
    margin-bottom: 3rem;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(200, 168, 130, 0.2);
}

.lesson-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(200, 168, 130, 0.2);
}

.lesson-title-section {
    text-align: center;
    margin: 2rem 0;
}

.lesson-meta {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.lesson-meta span {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: rgba(200, 168, 130, 0.1);
    border-radius: 20px;
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.course-title {
    color: var(--primary-gold);
    font-weight: 600;
}

.lesson-type {
    color: var(--primary-brown);
    background: rgba(186, 147, 109, 0.2) !important;
}

.lesson-duration {
    color: var(--text-secondary);
}

/* New styles for centered lesson number */
.lesson-number-centered {
    display: block;
    text-align: center;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--primary-brown);
    margin: 1rem 0 0.5rem 0;
    padding: 0.5rem 1rem;
    background: rgba(200, 168, 130, 0.1);
    border-radius: 20px;
    width: fit-content;
    margin-left: auto;
    margin-right: auto;
}

/* Duration positioned in lesson header navigation */
.lesson-duration-header {
    background: rgba(200, 168, 130, 0.9);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
}

.lesson-title {
    font-family: 'Crimson Text', serif;
    font-size: 2.5rem;
    color: var(--primary-brown);
    margin: 1rem 0;
    font-weight: 600;
    line-height: 1.2;
}

.lesson-actions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(200, 168, 130, 0.2);
}

.action-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: rgba(200, 168, 130, 0.1);
    border: 1px solid rgba(200, 168, 130, 0.3);
    border-radius: 8px;
    color: var(--primary-brown);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.action-btn:hover {
    background: rgba(200, 168, 130, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.lesson-progress-indicator {
    display: flex;
    align-items: center;
    gap: 1rem;
    position: fixed;
    top: 100px; /* Account for navbar height */
    right: 30px; /* More margin from edge */
    z-index: 100;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 1rem;
    border-radius: 50px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(200, 168, 130, 0.2);
    transition: all 0.3s ease;
}

.lesson-progress-indicator:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
}

.progress-circle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: conic-gradient(var(--primary-gold) var(--progress, 0%), rgba(200, 168, 130, 0.2) 0%);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.progress-circle::before {
    content: '';
    position: absolute;
    width: 46px;
    height: 46px;
    background: white;
    border-radius: 50%;
}

.progress-percent {
    position: relative;
    z-index: 1;
    font-weight: 600;
    color: var(--primary-brown);
    font-size: 0.9rem;
}

/* Lesson Content Area */
.lesson-content-area {
    position: relative;
}

.lesson-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 16px;
    margin: 2rem 0;
}

.loading-spinner {
    font-size: 2rem;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
    color: var(--primary-gold);
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.lesson-materials {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 16px;
    padding: 2rem;
    margin: 2rem 0;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(200, 168, 130, 0.2);
}

/* Material Items */
.material-item {
    margin-bottom: 3rem;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    border-left: 4px solid var(--primary-gold);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.material-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.material-title {
    font-family: 'Crimson Text', serif;
    font-size: 1.6rem;
    color: var(--primary-brown);
    margin-bottom: 1.5rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.material-title::before {
    content: '📖';
    font-size: 1.2rem;
}

.text-material .material-title::before {
    content: '📄';
}

.vocabulary-material .material-title::before {
    content: '📚';
}

.image-material .material-title::before {
    content: '🖼️';
}

.quiz-material .material-title::before {
    content: '❓';
}

.material-content {
    line-height: 1.8;
    color: var(--text-primary);
}

.material-content p {
    margin-bottom: 1rem;
}

.arabic-text {
    font-size: 1.4rem;
    line-height: 2;
    color: var(--primary-brown);
    font-weight: 500;
    text-align: right;
    background: rgba(200, 168, 130, 0.1);
    padding: 1rem;
    border-radius: 8px;
    margin: 1rem 0;
}

.english-text {
    font-size: 1.1rem;
    line-height: 1.7;
    color: var(--text-primary);
}

/* Lesson Navigation Footer */
.lesson-navigation-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 3rem;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(200, 168, 130, 0.2);
}

.lesson-actions-center {
    display: flex;
    gap: 1rem;
}

.nav-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    background: var(--primary-gold);
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.nav-btn:hover:not(:disabled) {
    background: var(--primary-brown);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.nav-btn:disabled {
    background: rgba(200, 168, 130, 0.3);
    color: rgba(107, 91, 74, 0.5);
    cursor: not-allowed;
}

.back-btn {
    background: rgba(200, 168, 130, 0.2);
    color: var(--primary-brown);
}

.back-btn:hover {
    background: rgba(200, 168, 130, 0.3);
}

.action-btn.complete-lesson {
    background: var(--success-color, #10b981);
    color: white;
}

.action-btn.restart-lesson {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border-color: rgba(239, 68, 68, 0.3);
}

/* Lesson Sidebar */
.lesson-sidebar {
    position: fixed;
    right: -400px;
    top: 0;
    width: 400px;
    height: 100vh;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: -8px 0 32px rgba(0, 0, 0, 0.1);
    transition: right 0.3s ease;
    z-index: 1000;
    padding: 2rem;
    overflow-y: auto;
}

.lesson-sidebar.active {
    right: 0;
}

.sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(200, 168, 130, 0.2);
}

.sidebar-header h3 {
    font-family: 'Crimson Text', serif;
    color: var(--primary-brown);
    margin: 0;
}

.close-sidebar {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.close-sidebar:hover {
    background: rgba(200, 168, 130, 0.1);
    color: var(--primary-brown);
}

.notes-textarea {
    width: 100%;
    min-height: 300px;
    border: 1px solid rgba(200, 168, 130, 0.3);
    border-radius: 8px;
    padding: 1rem;
    font-family: inherit;
    font-size: 1rem;
    line-height: 1.6;
    resize: vertical;
    margin-bottom: 1rem;
}

.sidebar-actions {
    display: flex;
    gap: 1rem;
}

.btn-secondary {
    padding: 0.75rem 1.5rem;
    background: rgba(200, 168, 130, 0.1);
    border: 1px solid rgba(200, 168, 130, 0.3);
    border-radius: 8px;
    color: var(--primary-brown);
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    padding: 0.75rem 1.5rem;
    background: #c49060;
    border: 2px solid #c49060;
    border-radius: 8px;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(196, 144, 96, 0.3);
}

.btn-primary:hover {
    background: #a67c52;
    border-color: #a67c52;
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(166, 124, 82, 0.4);
}

/* No Materials / Placeholder Content Styling */
.no-materials {
    text-align: center;
    padding: 4rem 2rem;
    background: linear-gradient(135deg, rgba(200, 168, 130, 0.1), rgba(186, 147, 109, 0.1));
    border: 2px dashed rgba(200, 168, 130, 0.3);
    border-radius: 12px;
    margin: 2rem 0;
}

.no-materials .ornament-top,
.no-materials .ornament-bottom {
    font-size: 2rem;
    color: var(--primary-gold);
    margin: 1rem 0;
    opacity: 0.8;
}

.no-materials h3 {
    color: var(--primary-brown);
    font-family: 'Crimson Text', serif;
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
    font-weight: 600;
}

.content-preview {
    background: rgba(255, 255, 255, 0.8);
    padding: 2rem;
    border-radius: 8px;
    margin: 2rem 0;
    text-align: left;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.content-preview h4 {
    color: var(--primary-brown);
    font-family: 'Crimson Text', serif;
    font-size: 1.3rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.content-preview ul {
    list-style: none;
    padding: 0;
}

.content-preview li {
    padding: 0.5rem 0;
    font-size: 1.05rem;
    color: var(--text-primary);
    line-height: 1.6;
}

.placeholder-message {
    margin-top: 2rem;
    padding: 1.5rem;
    background: rgba(186, 147, 109, 0.1);
    border-radius: 8px;
    border-left: 4px solid var(--primary-gold);
}

.placeholder-message p {
    color: var(--text-secondary);
    font-style: italic;
    margin: 0;
    line-height: 1.6;
}

/* Enhanced Placeholder Content */
.placeholder-text,
.placeholder-vocab p,
.no-content {
    color: var(--text-secondary);
    font-style: italic;
    padding: 1.5rem;
    background: rgba(200, 168, 130, 0.1);
    border-radius: 8px;
    border-left: 4px solid var(--primary-gold);
    margin: 1rem 0;
}

.placeholder-image {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    background: linear-gradient(135deg, rgba(200, 168, 130, 0.1), rgba(186, 147, 109, 0.1));
    border: 2px dashed rgba(200, 168, 130, 0.3);
    border-radius: 8px;
    color: var(--text-secondary);
    text-align: center;
    padding: 2rem;
}

.image-placeholder-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.7;
}

.placeholder-image p {
    font-style: italic;
    margin: 0;
    color: var(--text-secondary);
}

/* Sample Vocabulary */
.sample-vocab {
    margin-top: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 6px;
}

.vocab-item.sample {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: rgba(200, 168, 130, 0.1);
    border-radius: 6px;
    margin: 0.5rem 0;
}

.vocab-item.sample .arabic {
    font-size: 1.2rem;
    color: var(--primary-brown);
    font-weight: 500;
}

.vocab-item.sample .translation {
    color: var(--text-secondary);
    font-style: italic;
}

.sample-vocab-format {
    margin-top: 1rem;
    text-align: center;
}

.vocab-note {
    padding: 0.5rem;
    background: rgba(186, 147, 109, 0.1);
    border-radius: 4px;
    margin-top: 1rem;
}

.vocab-note small {
    color: var(--text-secondary);
    font-style: italic;
}

/* Enhanced Vocabulary Display */
.vocab-item {
    margin: 0.75rem 0;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    border-left: 3px solid var(--primary-gold);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.vocab-arabic {
    font-size: 1.3rem;
    color: var(--primary-brown);
    font-weight: 500;
    margin-bottom: 0.5rem;
    text-align: right;
}

.vocab-english {
    color: var(--text-primary);
    font-size: 1rem;
    line-height: 1.5;
}

/* Vocabulary Table Styling */
.vocab-table-container {
    margin: 1.5rem 0;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    overflow-x: auto;
}

.vocab-table {
    width: 100%;
    border-collapse: collapse;
    font-family: inherit;
}

.vocab-table th {
    background: linear-gradient(135deg, var(--primary-gold), rgba(200, 168, 130, 0.8));
    color: white;
    padding: 1rem 1.5rem;
    text-align: left;
    font-weight: 600;
    font-size: 1.1rem;
    border: none;
}

.vocab-header-arabic {
    text-align: right;
    border-top-right-radius: 8px;
}

.vocab-header-english {
    border-top-left-radius: 8px;
}

.vocab-table td {
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid rgba(200, 168, 130, 0.2);
    vertical-align: middle;
}

.vocab-table tbody tr {
    transition: background-color 0.2s ease;
}

.vocab-table tbody tr:hover {
    background: rgba(200, 168, 130, 0.1);
}

.vocab-table tbody tr:last-child td {
    border-bottom: none;
}

.vocab-table .vocab-arabic {
    font-size: 1.4rem;
    color: var(--primary-brown);
    font-weight: 600;
    text-align: right;
    margin: 0;
    line-height: 1.4;
}

.vocab-table .vocab-english {
    font-size: 1.1rem;
    color: var(--text-primary);
    margin: 0;
    line-height: 1.5;
    font-weight: 500;
}

/* Responsive vocabulary table */
@media (max-width: 768px) {
    .vocab-table-container {
        padding: 1rem;
    }
    
    .vocab-table th,
    .vocab-table td {
        padding: 0.75rem 1rem;
    }
    
    .vocab-table .vocab-arabic {
        font-size: 1.2rem;
    }
    
    .vocab-table .vocab-english {
        font-size: 1rem;
    }
}

/* Enhanced Quiz Preview Styling */
.quiz-question {
    margin: 1.5rem 0;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 12px;
    border: 1px solid rgba(200, 168, 130, 0.2);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.question-content {
    padding: 1.5rem;
    background: rgba(200, 168, 130, 0.1);
    border-bottom: 1px solid rgba(200, 168, 130, 0.2);
}

.question-content p {
    margin: 0;
    font-size: 1.1rem;
    color: var(--text-primary);
    line-height: 1.6;
    font-weight: 500;
}

.quiz-options-preview {
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

.option-placeholder {
    padding: 0.75rem 1rem;
    margin: 0.5rem 0;
    background: rgba(200, 168, 130, 0.1);
    border: 1px solid rgba(200, 168, 130, 0.3);
    border-radius: 6px;
    color: var(--text-secondary);
    font-style: italic;
    cursor: default;
}

.option-item {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(200, 168, 130, 0.2);
    border-radius: 8px;
    transition: all 0.2s ease;
}

.option-item:hover {
    background: rgba(200, 168, 130, 0.05);
}

.option-item.correct-option {
    background: rgba(16, 185, 129, 0.1);
    border-color: rgba(16, 185, 129, 0.3);
}

.option-marker {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(200, 168, 130, 0.1);
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.correct-option .option-marker {
    background: #10b981;
    color: white;
}

.option-text {
    flex: 1;
    font-size: 1rem;
    color: var(--text-primary);
}

.correct-option .option-text {
    color: #10b981;
    font-weight: 600;
}

.quiz-explanation {
    padding: 1rem 1.5rem;
    background: rgba(200, 168, 130, 0.05);
    border-top: 1px solid rgba(200, 168, 130, 0.1);
}

.quiz-explanation p {
    margin: 0;
    font-size: 0.95rem;
    color: var(--text-secondary);
    line-height: 1.5;
}

.quiz-note {
    text-align: center;
    padding: 1rem;
    background: rgba(186, 147, 109, 0.1);
    border-radius: 6px;
    margin-top: 1rem;
}

/* Interactive Quiz Styles */
.quiz-options {
    padding: 1rem 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

.quiz-option {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(200, 168, 130, 0.2);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.quiz-option:hover {
    background: rgba(200, 168, 130, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.quiz-option.selected {
    background: rgba(200, 168, 130, 0.15);
    border-color: rgba(200, 168, 130, 0.4);
}

.quiz-option.correct-answer {
    background: rgba(16, 185, 129, 0.1);
    border-color: rgba(16, 185, 129, 0.3);
}

.quiz-option.disabled {
    cursor: default;
    opacity: 0.8;
}

.quiz-option.disabled:hover {
    transform: none;
    box-shadow: none;
}

.option-indicator {
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(200, 168, 130, 0.1);
    border: 1px solid rgba(200, 168, 130, 0.3);
    color: var(--text-secondary);
    font-size: 1rem;
    transition: all 0.2s ease;
}

.quiz-option:hover .option-indicator {
    background: rgba(200, 168, 130, 0.2);
}

.option-text {
    flex: 1;
    font-size: 1.1rem;
    color: var(--text-primary);
    transition: all 0.2s ease;
}

.quiz-feedback {
    padding: 1rem 1.5rem;
    margin-top: 0.5rem;
    border-top: 1px dashed rgba(200, 168, 130, 0.3);
}

.correct-feedback {
    color: #10b981;
    padding: 0.75rem;
    background: rgba(16, 185, 129, 0.1);
    border-radius: 8px;
    font-weight: 500;
    text-align: center;
}

.incorrect-feedback {
    color: #ef4444;
    padding: 0.75rem;
    background: rgba(239, 68, 68, 0.1);
    border-radius: 8px;
    font-weight: 500;
    text-align: center;
}

.quiz-explanation {
    padding: 1rem 1.5rem;
    margin-top: 0.5rem;
    background: rgba(200, 168, 130, 0.05);
    border-top: 1px solid rgba(200, 168, 130, 0.1);
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
}

.quiz-explanation h4 {
    font-size: 1rem;
    color: var(--primary-brown);
    margin: 0 0 0.5rem 0;
}

.quiz-explanation p {
    margin: 0;
    font-size: 0.95rem;
    color: var(--text-secondary);
    line-height: 1.5;
}

/* Enhanced Image Display */
.image-container {
    margin: 1.5rem 0;
    text-align: center;
}

.lesson-image-container {
    position: relative;
    display: inline-block;
    max-width: 100%;
}

.lesson-image {
    max-width: 100%;
    height: auto;
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: zoom-in;
}

.lesson-image:hover {
    transform: scale(1.02);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.2);
}

.image-fallback {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 38, 0.1));
    border: 2px dashed rgba(239, 68, 68, 0.3);
    border-radius: 8px;
    color: #dc2626;
    text-align: center;
    padding: 2rem;
}

.image-fallback .image-placeholder-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.7;
}

.image-caption {
    margin-top: 1rem;
    padding: 1rem;
    background: rgba(200, 168, 130, 0.1);
    border-radius: 8px;
    font-style: italic;
    color: var(--text-secondary);
    line-height: 1.6;
}

/* User-side Image Sizing Support */
.lesson-image-container.size-small {
    max-width: 300px;
}

.lesson-image-container.size-medium {
    max-width: 500px;
}

.lesson-image-container.size-large {
    max-width: 700px;
}

.lesson-image-container.size-full {
    max-width: 100%;
}

/* Responsive adjustments for user-side images */
@media (max-width: 768px) {
    .lesson-image-container.size-large,
    .lesson-image-container.size-full {
        max-width: 100%;
    }
    
    .lesson-image-container.size-medium {
        max-width: 90%;
    }
    
    .lesson-image-container.size-small {
        max-width: 80%;
    }
}

/* Progress Dots Enhancement */
.progress-dots {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.progress-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(200, 168, 130, 0.3);
    transition: all 0.3s ease;
    cursor: pointer;
}

.progress-dot.active {
    background: var(--primary-gold);
    transform: scale(1.3);
    box-shadow: 0 0 0 3px rgba(200, 168, 130, 0.3);
}

.progress-dot.completed {
    background: var(--success-color, #10b981);
}

.progress-dot:hover {
    transform: scale(1.2);
}

/* Mobile Responsive Improvements */
@media (max-width: 768px) {
    .lesson-header {
        padding: 1.5rem;
    }
    
    .lesson-meta {
        flex-direction: column;
        gap: 1rem;
        align-items: center;
    }
    
    .lesson-title {
        font-size: 2rem;
    }
    
    .lesson-materials {
        padding: 1.5rem;
    }
    
    .material-item {
        padding: 1.5rem;
    }
    
    .lesson-navigation-footer {
        flex-direction: column;
        gap: 1rem;
    }
    
    .lesson-actions-center {
        order: -1;
    }
    
    .nav-btn {
        width: 100%;
        justify-content: center;
    }
    
    .lesson-sidebar {
        width: 100%;
        right: -100%;
    }
    
    /* Adjust fixed progress indicator for mobile */
    .lesson-progress-indicator {
        top: 80px; /* Account for navbar on mobile */
        right: 15px; /* Proper margin for mobile */
        padding: 0.75rem;
        transform: scale(0.9);
    }
    
    .progress-circle {
        width: 50px;
        height: 50px;
    }
    
    .progress-circle::before {
        width: 38px;
        height: 38px;
    }
    
    .progress-percent {
        font-size: 0.8rem;
    }
    
    /* Mobile responsive for new elements */
    .lesson-number-centered {
        font-size: 1rem;
        margin: 0.5rem auto;
    }
    
    .lesson-duration-header {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }
    
    .lesson-navigation {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .lesson-navigation .lesson-duration-header {
        align-self: center;
    }
}

/* ===========================================
   LESSON PREVIEW MODAL
   =========================================== */

.preview-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    box-sizing: border-box;
}

.preview-modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.preview-modal-content {
    position: relative;
    width: 100%;
    max-width: 1200px;
    max-height: 90vh;
    background: var(--bg-primary);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.preview-modal-header {
    background: var(--primary-brown);
    color: white;
    padding: 1.5rem 2rem;
    position: relative;
    border-bottom: 3px solid var(--primary-gold);
}

.preview-modal-title {
    font-family: 'Crimson Text', serif;
    font-size: 1.8rem;
    margin: 0 0 0.5rem 0;
    color: white;
}

.preview-modal-subtitle {
    font-size: 1rem;
    margin: 0;
    opacity: 0.9;
    color: var(--bg-secondary);
}

.preview-close-btn {
    position: absolute;
    top: 1rem;
    right: 1.5rem;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    font-size: 2rem;
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.preview-close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.preview-lesson-container {
    flex: 1;
    overflow-y: auto;
    padding: 2rem;
    background: var(--bg-primary);
}

/* Preview Lesson Styles - Mirror the user-side lesson styles */

.preview-lesson-header {
    margin-bottom: 2rem;
}

.preview-lesson-title-section {
    text-align: center;
    margin: 1rem 0 2rem 0;
}

.preview-lesson-meta {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.preview-lesson-meta span {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: rgba(200, 168, 130, 0.1);
    border-radius: 20px;
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.preview-course-title {
    color: var(--primary-gold) !important;
    font-weight: 600 !important;
}

.preview-lesson-duration {
    color: var(--text-secondary) !important;
}

.preview-lesson-title {
    font-family: 'Crimson Text', serif;
    font-size: 2.5rem;
    color: var(--primary-brown);
    margin: 1rem 0;
    font-weight: 600;
    line-height: 1.2;
}

.preview-lesson-description {
    margin-bottom: 1rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.preview-lesson-description p {
    color: var(--text-primary);
    line-height: 1.6;
    font-size: 1.1rem;
}

.preview-title-underline {
    width: 100px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-gold), var(--primary-brown));
    margin: 1rem auto;
    border-radius: 2px;
}

.preview-lesson-content-area {
    margin-top: 2rem;
}

.preview-lesson-loading {
    text-align: center;
    padding: 3rem;
    color: var(--text-secondary);
}

.preview-lesson-loading .loading-spinner {
    font-size: 2rem;
    animation: spin 1s linear infinite;
    display: block;
    margin-bottom: 1rem;
}

.preview-lesson-materials {
    max-width: 900px;
    margin: 0 auto;
}

/* Material Items in Preview - Reuse existing styles */
.preview-lesson-materials .material-item {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(200, 168, 130, 0.2);
    transition: all 0.3s ease;
}

.preview-lesson-materials .material-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.preview-lesson-materials .material-title {
    font-family: 'Crimson Text', serif;
    font-size: 1.8rem;
    color: var(--primary-brown);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.preview-lesson-materials .material-title::before {
    content: '📖';
    font-size: 1.2rem;
}

.preview-lesson-materials .text-material .material-title::before {
    content: '📄';
}

.preview-lesson-materials .vocabulary-material .material-title::before {
    content: '📚';
}

.preview-lesson-materials .image-material .material-title::before {
    content: '🖼️';
}

.preview-lesson-materials .quiz-material .material-title::before {
    content: '❓';
}

.preview-lesson-materials .material-content {
    line-height: 1.8;
    color: var(--text-primary);
}

.preview-lesson-materials .material-content p {
    margin-bottom: 1rem;
}

.preview-lesson-materials .arabic-text {
    font-size: 1.4rem;
    line-height: 2;
    color: var(--primary-brown);
    font-weight: 500;
    text-align: right;
    background: rgba(200, 168, 130, 0.1);
    padding: 1rem;
    border-radius: 8px;
    margin: 1rem 0;
    direction: rtl;
}

.preview-lesson-materials .english-text {
    font-size: 1.1rem;
    line-height: 1.7;
    color: var(--text-primary);
}

/* Vocabulary Table in Preview */
.preview-lesson-materials .vocab-table-container {
    margin: 1rem 0;
    overflow-x: auto;
}

.preview-lesson-materials .vocab-table {
    width: 100%;
    border-collapse: collapse;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.preview-lesson-materials .vocab-table th {
    background: var(--primary-brown);
    color: white;
    padding: 1rem;
    text-align: left;
    font-weight: 600;
}

.preview-lesson-materials .vocab-table td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid rgba(200, 168, 130, 0.2);
}

.preview-lesson-materials .vocab-table .vocab-arabic {
    font-size: 1.2rem;
    color: var(--primary-brown);
    font-weight: 500;
    text-align: right;
    direction: rtl;
}

.preview-lesson-materials .vocab-table .vocab-english {
    color: var(--text-primary);
}

/* Image Materials in Preview */
.preview-lesson-materials .image-container {
    text-align: center;
}

.preview-lesson-materials .lesson-image {
    max-width: 100%;
    max-height: 400px;
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    margin: 1rem 0;
}

.preview-lesson-materials .image-caption {
    font-style: italic;
    color: var(--text-secondary);
    margin-top: 0.5rem;
}

.preview-lesson-materials .placeholder-image {
    background: rgba(200, 168, 130, 0.1);
    border: 2px dashed rgba(200, 168, 130, 0.4);
    border-radius: 12px;
    padding: 3rem;
    text-align: center;
    margin: 1rem 0;
}

.preview-lesson-materials .image-placeholder-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.6;
}

/* Quiz Materials in Preview */
.preview-lesson-materials .quiz-content {
    background: rgba(147, 51, 234, 0.05);
    border-left: 4px solid #7c3aed;
    padding: 1.5rem;
    border-radius: 8px;
}

.preview-lesson-materials .quiz-question {
    margin-bottom: 1rem;
}

.preview-lesson-materials .question-content p {
    font-size: 1.2rem;
    font-weight: 500;
    color: var(--primary-brown);
    margin-bottom: 1rem;
}

.preview-lesson-materials .quiz-options-preview {
    margin: 1rem 0;
}

.preview-lesson-materials .option-placeholder {
    background: rgba(255, 255, 255, 0.8);
    border: 2px solid rgba(200, 168, 130, 0.3);
    border-radius: 8px;
    padding: 0.75rem 1rem;
    margin: 0.5rem 0;
    color: var(--text-primary);
    transition: all 0.2s ease;
}

.preview-lesson-materials .option-placeholder:hover {
    border-color: var(--primary-gold);
    background: rgba(200, 168, 130, 0.1);
}

.preview-lesson-materials .quiz-note {
    font-size: 0.9rem;
    color: var(--text-secondary);
    text-align: center;
    margin-top: 1rem;
    padding: 0.75rem;
    background: rgba(200, 168, 130, 0.1);
    border-radius: 6px;
}

/* Placeholder content styles */
.preview-lesson-materials .placeholder-text,
.preview-lesson-materials .placeholder-vocab {
    background: rgba(200, 168, 130, 0.1);
    border: 2px dashed rgba(200, 168, 130, 0.4);
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
}

.preview-lesson-materials .no-materials {
    text-align: center;
    padding: 3rem;
    color: var(--text-secondary);
}

.preview-lesson-materials .no-materials .ornament-top,
.preview-lesson-materials .no-materials .ornament-bottom {
    font-size: 2rem;
    color: var(--primary-gold);
    margin: 1rem 0;
}

/* Responsive design for preview modal */
@media (max-width: 768px) {
    .preview-modal {
        padding: 1rem;
    }
    
    .preview-modal-content {
        max-height: 95vh;
    }
    
    .preview-modal-header {
        padding: 1rem 1.5rem;
    }
    
    .preview-modal-title {
        font-size: 1.5rem;
    }
    
    .preview-lesson-container {
        padding: 1rem;
    }
    
    .preview-lesson-title {
        font-size: 2rem;
    }
    
    .preview-lesson-meta {
        gap: 1rem;
    }
    
    .preview-lesson-materials .material-item {
        padding: 1.5rem;
    }
}

/* ===========================================
   END LESSON PREVIEW MODAL
   =========================================== */

/* Material Status Indicators */
.material-status {
    font-size: 0.75rem;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 500;
    margin-left: 8px;
}

.material-status.existing {
    background-color: #e3f2fd;
    color: #1976d2;
    border: 1px solid #bbdefb;
}

.material-status.new {
    background-color: #e8f5e8;
    color: #388e3c;
    border: 1px solid #c8e6c9;
}

.existing-material {
    border-left: 3px solid #1976d2;
}

.new-material {
    border-left: 3px solid #388e3c;
}

/* Cancel Edit Button */
#cancelEditBtn {
    background-color: #ff9800;
    color: white;
    border: none;
    margin-left: 8px;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

#cancelEditBtn:hover {
    background-color: #f57c00;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(245, 124, 0, 0.3);
}

/* Cancel Course Edit Button */
#cancelCourseEditBtn {
    background-color: #e53e3e;
    color: white;
    border: 2px solid #e53e3e;
    margin-left: 8px;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(229, 62, 62, 0.2);
}

#cancelCourseEditBtn:hover {
    background-color: #c53030;
    border-color: #c53030;
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(197, 48, 48, 0.4);
}

/* Update Lesson Button Style */
.btn-primary.update-lesson {
    background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
}

.btn-primary.update-lesson:hover {
    background: linear-gradient(135deg, #f57c00 0%, #ef6c00 100%);
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(239, 108, 0, 0.3);
}

/* Update Course Button Style */
.btn-primary.update-course {
    background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
}

.btn-primary.update-course:hover {
    background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(25, 118, 210, 0.3);
}

/* ===========================================
   VOCABULARY TABLE BUILDER
   =========================================== */

.vocabulary-table-builder {
    margin-top: 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.95);
    overflow: hidden;
}

.vocab-table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    border-bottom: 1px solid #e2e8f0;
}

.vocab-table-header h5 {
    margin: 0;
    color: #2d3748;
    font-weight: 600;
    font-size: 1rem;
}

.add-vocab-row {
    background: #48bb78;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.add-vocab-row:hover {
    background: #38a169;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(72, 187, 120, 0.3);
}

.vocab-table-container {
    max-height: 400px;
    overflow-y: auto;
}

.vocab-builder-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.vocab-builder-table th {
    background: #f7fafc;
    color: #2d3748;
    font-weight: 600;
    padding: 0.75rem;
    text-align: left;
    border-bottom: 2px solid #e2e8f0;
    position: sticky;
    top: 0;
    z-index: 1;
}

.vocab-header-arabic {
    width: 40%;
    text-align: right;
}

.vocab-header-english {
    width: 40%;
}

.vocab-header-actions {
    width: 20%;
    text-align: center;
}

.vocab-row {
    transition: background-color 0.2s ease;
}

.vocab-row:hover {
    background-color: #f9fafb;
}

.vocab-row:nth-child(even) {
    background-color: #fafafa;
}

.vocab-row:nth-child(even):hover {
    background-color: #f5f5f5;
}

.vocab-cell-arabic,
.vocab-cell-english,
.vocab-cell-actions {
    padding: 0.5rem;
    border-bottom: 1px solid #e2e8f0;
    vertical-align: middle;
}

.vocab-input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    font-size: 0.875rem;
    background: white;
    transition: all 0.2s ease;
}

.vocab-input:focus {
    outline: none;
    border-color: #3182ce;
    box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
}

.arabic-input {
    font-family: 'Arial', sans-serif;
    text-align: right;
    direction: rtl;
    font-size: 1rem;
}

.english-input {
    font-family: 'Inter', sans-serif;
}

.vocab-cell-actions {
    text-align: center;
}

.delete-vocab-row {
    background: #e53e3e;
    color: white;
    border: none;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
}

.delete-vocab-row:hover:not(:disabled) {
    background: #c53030;
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(229, 62, 62, 0.3);
}

.delete-vocab-row:disabled {
    background: #cbd5e0;
    cursor: not-allowed;
    opacity: 0.6;
}

/* Mobile responsiveness for vocabulary table */
@media (max-width: 768px) {
    .vocab-table-header {
        flex-direction: column;
        gap: 0.5rem;
        align-items: stretch;
    }
    
    .vocab-builder-table {
        font-size: 0.8rem;
    }
    
    .vocab-header-arabic,
    .vocab-header-english {
        width: 45%;
    }
    
    .vocab-header-actions {
        width: 10%;
    }
    
    .vocab-input {
        padding: 0.4rem;
        font-size: 0.8rem;
    }
    
    .delete-vocab-row {
        width: 25px;
        height: 25px;
        font-size: 0.8rem;
    }
}