<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IQA - Interactive Quranic Arabic Learning</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Crimson+Text:wght@400;600&family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation Header -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <h1>IQA</h1>
                <span class="nav-subtitle">INTERACTIVE QURANIC ARABIC</span>
            </div>
            <div class="nav-links">
                <a href="courses.html" class="nav-link">Courses</a>
                <a href="admin.html" class="nav-link">Admin</a>
                <a href="about.html" class="nav-link">About Us</a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <div class="content-container">
            <div class="content-card">
                <!-- Decorative ornament -->
                <div class="ornament-top">✦</div>
                
                <h1 class="main-title">Quranic Arabic</h1>
                <div class="title-underline"></div>
                <h2 class="sub-title">your Time, your Pace</h2>
                
                <p class="description">
                    IQA is a fully interactive platform designed to teach Quranic Arabic from the 
                    ground up. Through engaging lessons, quizzes, flashcards, and practical 
                    examples, learners will master grammar (nahw), morphology (sarf), and 
                    vocabulary to understand the Qur'an — all at their own pace.
                </p>
                
                <div class="button-container">
                    <a href="login.html" class="sign-in-btn">
                        <span class="btn-text">Sign In / Sign Up</span>
                        <span class="btn-arrow">→</span>
                    </a>
                </div>
                
                <!-- Decorative ornament -->
                <div class="ornament-bottom">✦</div>
            </div>
        </div>
    </main>

    <script src="utils.js"></script>
    <script src="script.js"></script>
</body>
</html>